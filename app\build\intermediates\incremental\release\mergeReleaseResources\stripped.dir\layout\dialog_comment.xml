<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?colorBackground"
    android:padding="16dp">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/text_comment_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="?textColorPrimary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:text="评论" />

        <TextView
            android:id="@+id/text_comment_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/text_comment_title"
            android:layout_centerVertical="true"
            android:layout_marginStart="8dp"
            android:textColor="?textColorSecondary"
            android:textSize="14sp"
            tools:text="(1024)" />

        <ImageButton
            android:id="@+id/button_comment_close"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭"
            android:src="@drawable/ic_close" />
    </RelativeLayout>

    <!-- 加载中视图 -->
    <com.example.aimusicplayer.ui.widget.LottieLoadingView
        android:id="@+id/loading_view_comment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        app:lottieAnimationAsset="data_loading.json"
        app:loadingMessage="正在加载评论..."
        app:autoPlay="true"
        app:loop="true" />

    <!-- 空列表提示 -->
    <TextView
        android:id="@+id/text_empty_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="32dp"
        android:text="暂无评论"
        android:textColor="?textColorSecondary"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="gone" />

    <!-- 评论列表 - 添加下拉刷新功能 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_comment"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_comment"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clipToPadding="false"
            android:paddingBottom="8dp"
            android:maxHeight="400dp"
            android:layoutAnimation="@anim/layout_animation_comment_list"
            tools:listitem="@layout/item_comment" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载更多进度条 -->
    <FrameLayout
        android:id="@+id/layout_load_more"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="8dp"
        android:visibility="gone">

        <ProgressBar
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:indeterminateTint="?colorAccent" />
    </FrameLayout>

    <!-- 评论输入区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical">

        <EditText
            android:id="@+id/edit_text_comment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@drawable/bg_comment_input"
            android:hint="发表评论..."
            android:padding="12dp"
            android:maxLines="3"
            android:textSize="14sp"
            android:inputType="textMultiLine"
            android:importantForAutofill="no" />

        <Button
            android:id="@+id/btn_send_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="发送"
            android:textColor="@android:color/white"
            android:background="@drawable/bg_button_primary" />
    </LinearLayout>

    <!-- 评论发送成功提示 -->
    <TextView
        android:id="@+id/text_comment_success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="8dp"
        android:background="@drawable/bg_comment_success"
        android:paddingHorizontal="16dp"
        android:paddingVertical="8dp"
        android:text="评论发送成功"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        android:visibility="gone" />
</LinearLayout>
