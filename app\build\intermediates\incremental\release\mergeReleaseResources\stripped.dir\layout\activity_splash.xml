<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/splash_background"
    tools:context=".SplashActivity">

    <!-- 添加渐变背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/splash_gradient" />

    <!-- 创建一个容器来包含图标和文字，使其作为整体垂直居中 -->
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:layout_centerInParent="true">

    <ImageView
        android:id="@+id/splashImageView"
            android:layout_width="140dp"
            android:layout_height="140dp"
            android:contentDescription="@string/app_name"
            android:src="@drawable/logo_music"
            android:layout_marginBottom="32dp" />

    <TextView
        android:id="@+id/app_name_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
            android:text="轻聆"
        android:textColor="@color/text_light"
            android:textSize="60sp"
            android:textStyle="bold"
            android:shadowColor="#80000000"
            android:shadowDx="2"
            android:shadowDy="2"
            android:shadowRadius="4"
            android:layout_marginBottom="20dp" />

    <TextView
        android:id="@+id/welcome_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
            android:text="您的专属智能车载音乐伴侣"
            android:textColor="@color/text_light"
            android:alpha="0.88"
            android:textSize="20sp"
            android:shadowColor="#40000000"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="2" />

    </LinearLayout>

    <!-- 版本信息 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="24dp"
        android:text="Version 1.0"
        android:textColor="@color/text_light"
        android:alpha="0.8"
        android:textSize="14sp" />

</RelativeLayout>