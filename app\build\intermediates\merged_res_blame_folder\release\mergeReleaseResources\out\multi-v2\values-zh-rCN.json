{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeReleaseResources-60:/values-zh-rCN/values-zh-rCN.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,295,395,477,574,680,757,832,923,1016,1113,1209,1303,1396,1491,1583,1674,1765,1843,1939,2034,2129,2226,2322,2420,2568,2662", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "195,290,390,472,569,675,752,827,918,1011,1108,1204,1298,1391,1486,1578,1669,1760,1838,1934,2029,2124,2221,2317,2415,2563,2657,2736"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "850,945,1040,1140,1222,1319,1425,1502,1577,1668,1761,1858,1954,2048,2141,2236,2328,2419,2510,2588,2684,2779,2874,2971,3067,3165,3313,12164", "endColumns": "94,94,99,81,96,105,76,74,90,92,96,95,93,92,94,91,90,90,77,95,94,94,96,95,97,147,93,78", "endOffsets": "940,1035,1135,1217,1314,1420,1497,1572,1663,1756,1853,1949,2043,2136,2231,2323,2414,2505,2583,2679,2774,2869,2966,3062,3160,3308,3402,12238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,111,167,225,278,350,404,479,557", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "106,162,220,273,345,399,474,552,611"}, "to": {"startLines": "85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "5860,5916,5972,6030,6083,6155,6209,6284,6362", "endColumns": "55,55,57,52,71,53,74,77,58", "endOffsets": "5911,5967,6025,6078,6150,6204,6279,6357,6416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,287,450,608,678,747,815,892,968,1022,1084,1158,1232,1294,1355,1414,1480,1568,1651,1739,1802,1869,1934,1988,2062,2135,2196,2258,2310,2368,2415,2476,2533,2595,2652,2713,2769,2824,2887,2949,3012,3061,3112,3177,3242", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "282,445,603,673,742,810,887,963,1017,1079,1153,1227,1289,1350,1409,1475,1563,1646,1734,1797,1864,1929,1983,2057,2130,2191,2253,2305,2363,2410,2471,2528,2590,2647,2708,2764,2819,2882,2944,3007,3056,3107,3172,3237,3286"}, "to": {"startLines": "2,11,15,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,382,545,4210,4280,4349,4417,4494,4570,4624,4686,4760,4834,4896,4957,5016,5082,5170,5253,5341,5404,5471,5536,5590,5664,5737,5798,6421,6473,6531,6578,6639,6696,6758,6815,6876,6932,6987,7050,7112,7175,7224,7275,7340,7405", "endLines": "10,14,18,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,69,68,67,76,75,53,61,73,73,61,60,58,65,87,82,87,62,66,64,53,73,72,60,61,51,57,46,60,56,61,56,60,55,54,62,61,62,48,50,64,64,48", "endOffsets": "377,540,698,4275,4344,4412,4489,4565,4619,4681,4755,4829,4891,4952,5011,5077,5165,5248,5336,5399,5466,5531,5585,5659,5732,5793,5855,6468,6526,6573,6634,6691,6753,6810,6871,6927,6982,7045,7107,7170,7219,7270,7335,7400,7449"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,156", "endColumns": "100,102", "endOffsets": "151,254"}, "to": {"startLines": "176,177", "startColumns": "4,4", "startOffsets": "11891,11992", "endColumns": "100,102", "endOffsets": "11987,12090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,116,183,248,312,381,453,529", "endColumns": "60,66,64,63,68,71,75,79", "endOffsets": "111,178,243,307,376,448,524,604"}, "to": {"startLines": "50,59,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3407,4081,8482,8547,8611,8680,8752,8828", "endColumns": "60,66,64,63,68,71,75,79", "endOffsets": "3463,4143,8542,8606,8675,8747,8823,8903"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\8c8cb4a267669986cd76225679b8b78b\\transformed\\zxing-android-embedded-4.2.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,104,151,238", "endColumns": "48,46,86,68", "endOffsets": "99,146,233,302"}, "to": {"startLines": "181,182,183,184", "startColumns": "4,4,4,4", "startOffsets": "12344,12393,12440,12527", "endColumns": "48,46,86,68", "endOffsets": "12388,12435,12522,12591"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,247,311,373,443,513,590,681,787,860,922,999,1058,1117,1195,1256,1313,1369,1428,1486,1540,1625,1681,1739,1793,1858,1950,2024,2100,2222,2284,2346,2445,2524,2598,2648,2699,2765,2829,2898,2976,3047,3108,3179,3246,3306,3392,3471,3538,3621,3706,3780,3845,3921,3969,4042,4106,4182,4260,4322,4386,4449,4514,4594,4670,4748,4824,4878,4933", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "242,306,368,438,508,585,676,782,855,917,994,1053,1112,1190,1251,1308,1364,1423,1481,1535,1620,1676,1734,1788,1853,1945,2019,2095,2217,2279,2341,2440,2519,2593,2643,2694,2760,2824,2893,2971,3042,3103,3174,3241,3301,3387,3466,3533,3616,3701,3775,3840,3916,3964,4037,4101,4177,4255,4317,4381,4444,4509,4589,4665,4743,4819,4873,4928,4997"}, "to": {"startLines": "19,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "703,3468,3532,3594,3664,3734,3811,3902,4008,4148,7454,7531,7590,7649,7727,7788,7845,7901,7960,8018,8072,8157,8213,8271,8325,8390,8908,8982,9058,9180,9242,9304,9403,9482,9556,9606,9657,9723,9787,9856,9934,10005,10066,10137,10204,10264,10350,10429,10496,10579,10664,10738,10803,10879,10927,11000,11064,11140,11218,11280,11344,11407,11472,11552,11628,11706,11782,11836,12095", "endLines": "22,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "endColumns": "12,63,61,69,69,76,90,105,72,61,76,58,58,77,60,56,55,58,57,53,84,55,57,53,64,91,73,75,121,61,61,98,78,73,49,50,65,63,68,77,70,60,70,66,59,85,78,66,82,84,73,64,75,47,72,63,75,77,61,63,62,64,79,75,77,75,53,54,68", "endOffsets": "845,3527,3589,3659,3729,3806,3897,4003,4076,4205,7526,7585,7644,7722,7783,7840,7896,7955,8013,8067,8152,8208,8266,8320,8385,8477,8977,9053,9175,9237,9299,9398,9477,9551,9601,9652,9718,9782,9851,9929,10000,10061,10132,10199,10259,10345,10424,10491,10574,10659,10733,10798,10874,10922,10995,11059,11135,11213,11275,11339,11402,11467,11547,11623,11701,11777,11831,11886,12159"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-zh-rCN\\values-zh-rCN.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "180", "startColumns": "4", "startOffsets": "12243", "endColumns": "100", "endOffsets": "12339"}}]}]}