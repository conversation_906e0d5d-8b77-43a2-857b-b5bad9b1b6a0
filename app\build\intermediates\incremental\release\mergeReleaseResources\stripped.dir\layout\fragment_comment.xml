<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_dark">

    <!-- 顶部标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_title_bar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/background_dark_lighter"
        app:layout_constraintTop_toTopOf="parent">

        <ImageButton
            android:id="@+id/btn_back"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/back"
            android:padding="12dp"
            android:src="@drawable/ic_back"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />

        <TextView
            android:id="@+id/text_comment_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="end"
            android:gravity="center"
            android:maxLines="1"
            android:textColor="@color/white"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="《歌曲名》的评论" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 评论列表 - 添加下拉刷新功能 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_title_bar">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_comments"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            android:layoutAnimation="@anim/layout_animation_comment_list"
            tools:listitem="@layout/item_comment" />
    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载更多进度条 -->
    <ProgressBar
        android:id="@+id/load_more_progress"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginBottom="16dp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible" />

    <!-- 空评论提示 -->
    <TextView
        android:id="@+id/text_empty_comment"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/no_comments"
        android:textColor="@color/text_secondary"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_title_bar"
        tools:visibility="visible" />

    <!-- 加载进度条 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_title_bar"
        tools:visibility="visible" />

    <!-- 底部评论输入框 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_comment_input"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/background_dark_lighter"
        android:padding="8dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <EditText
            android:id="@+id/edit_comment"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_comment_input"
            android:hint="@string/comment_hint"
            android:importantForAutofill="no"
            android:inputType="textMultiLine"
            android:maxLines="3"
            android:padding="12dp"
            android:textColor="@color/white"
            android:textColorHint="@color/text_hint"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_send_comment"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <Button
            android:id="@+id/btn_send_comment"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="@drawable/bg_button_primary"
            android:text="@string/send"
            android:textColor="@color/white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/edit_comment"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
