<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="12dp">

    <ImageView
        android:id="@+id/image_song_cover"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:contentDescription="@string/album_cover"
        android:scaleType="centerCrop"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/default_album_art" />

    <TextView
        android:id="@+id/text_song_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?android:attr/textColorPrimary"
        android:textSize="16sp"
        app:layout_constraintEnd_toStartOf="@+id/text_song_duration"
        app:layout_constraintStart_toEndOf="@+id/image_song_cover"
        app:layout_constraintTop_toTopOf="@+id/image_song_cover"
        tools:text="歌曲名称" />

    <TextView
        android:id="@+id/text_song_artist"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="8dp"
        android:ellipsize="end"
        android:maxLines="1"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="14sp"
        app:layout_constraintEnd_toStartOf="@+id/text_song_duration"
        app:layout_constraintStart_toStartOf="@+id/text_song_title"
        app:layout_constraintTop_toBottomOf="@+id/text_song_title"
        tools:text="歌手名称" />

    <TextView
        android:id="@+id/text_vip_tag"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:background="@drawable/bg_vip_tag"
        android:paddingStart="4dp"
        android:paddingTop="1dp"
        android:paddingEnd="4dp"
        android:paddingBottom="1dp"
        android:text="@string/vip"
        android:textColor="@color/colorAccent"
        android:textSize="10sp"
        android:visibility="gone"
        app:layout_constraintStart_toStartOf="@+id/text_song_artist"
        app:layout_constraintTop_toBottomOf="@+id/text_song_artist"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/text_song_duration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="?android:attr/textColorSecondary"
        android:textSize="14sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:text="03:45" />

</androidx.constraintlayout.widget.ConstraintLayout>
