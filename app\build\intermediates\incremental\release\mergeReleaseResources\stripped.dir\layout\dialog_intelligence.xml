<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="?colorBackground"
    android:padding="16dp">

    <!-- 标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp">

        <TextView
            android:id="@+id/text_intelligence_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerVertical="true"
            android:textColor="?textColorPrimary"
            android:textSize="18sp"
            android:textStyle="bold"
            android:text="心动模式推荐" />

        <ImageButton
            android:id="@+id/btn_close_intelligence"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="关闭"
            android:src="@drawable/ic_close" />
    </RelativeLayout>

    <!-- 加载中视图 -->
    <com.example.aimusicplayer.ui.widget.LottieLoadingView
        android:id="@+id/loading_view_intelligence"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        app:lottieAnimationAsset="music_loading.json"
        app:loadingMessage="正在加载心动模式推荐..."
        app:autoPlay="true"
        app:loop="true" />

    <!-- 空列表提示 -->
    <TextView
        android:id="@+id/text_empty_intelligence"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:padding="32dp"
        android:text="暂无推荐歌曲"
        android:textColor="?textColorSecondary"
        android:textSize="16sp"
        android:visibility="gone"
        tools:visibility="gone" />

    <!-- 推荐歌曲列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_intelligence"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:clipToPadding="false"
        android:paddingBottom="8dp"
        android:maxHeight="400dp"
        tools:listitem="@layout/item_playlist_song" />

</LinearLayout>
