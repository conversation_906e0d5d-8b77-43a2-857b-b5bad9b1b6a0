{"logs": [{"outputFile": "com.example.aimusicplayer.app-mergeReleaseResources-60:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\48fbfb4201531ba0d2c54a69b6a94add\\transformed\\core-1.9.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "180", "startColumns": "4", "startOffsets": "14240", "endColumns": "100", "endOffsets": "14336"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\83271d0c85512209c52890db5dc246bd\\transformed\\material-1.11.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,269,353,436,518,633,728,835,948,1033,1096,1190,1256,1318,1421,1487,1558,1617,1693,1758,1812,1925,1983,2044,2098,2177,2293,2376,2467,2609,2688,2767,2896,2984,3068,3125,3177,3243,3323,3413,3497,3576,3653,3730,3807,3876,3993,4092,4169,4262,4357,4431,4512,4608,4659,4743,4811,4897,4985,5048,5113,5176,5244,5349,5454,5549,5652,5713,5769", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "264,348,431,513,628,723,830,943,1028,1091,1185,1251,1313,1416,1482,1553,1612,1688,1753,1807,1920,1978,2039,2093,2172,2288,2371,2462,2604,2683,2762,2891,2979,3063,3120,3172,3238,3318,3408,3492,3571,3648,3725,3802,3871,3988,4087,4164,4257,4352,4426,4507,4603,4654,4738,4806,4892,4980,5043,5108,5171,5239,5344,5449,5544,5647,5708,5764,5846"}, "to": {"startLines": "19,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "842,3836,3920,4003,4085,4200,4295,4402,4515,4686,8649,8743,8809,8871,8974,9040,9111,9170,9246,9311,9365,9478,9536,9597,9651,9730,10352,10435,10526,10668,10747,10826,10955,11043,11127,11184,11236,11302,11382,11472,11556,11635,11712,11789,11866,11935,12052,12151,12228,12321,12416,12490,12571,12667,12718,12802,12870,12956,13044,13107,13172,13235,13303,13408,13513,13608,13711,13772,14075", "endLines": "22,51,52,53,54,55,56,57,58,60,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,178", "endColumns": "12,83,82,81,114,94,106,112,84,62,93,65,61,102,65,70,58,75,64,53,112,57,60,53,78,115,82,90,141,78,78,128,87,83,56,51,65,79,89,83,78,76,76,76,68,116,98,76,92,94,73,80,95,50,83,67,85,87,62,64,62,67,104,104,94,102,60,55,81", "endOffsets": "1006,3915,3998,4080,4195,4290,4397,4510,4595,4744,8738,8804,8866,8969,9035,9106,9165,9241,9306,9360,9473,9531,9592,9646,9725,9841,10430,10521,10663,10742,10821,10950,11038,11122,11179,11231,11297,11377,11467,11551,11630,11707,11784,11861,11930,12047,12146,12223,12316,12411,12485,12566,12662,12713,12797,12865,12951,13039,13102,13167,13230,13298,13403,13508,13603,13706,13767,13823,14152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\78bf313ee09e26c8c671a0bbc2457ec1\\transformed\\media3-ui-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,11,15,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,316,543,747,842,939,1021,1118,1216,1293,1357,1460,1561,1626,1689,1749,1820,1942,2067,2187,2255,2342,2418,2494,2587,2683,2750,2814,2867,2925,2975,3036,3096,3158,3222,3284,3343,3408,3474,3538,3605,3659,3718,3791,3864", "endLines": "10,14,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "311,538,742,837,934,1016,1113,1211,1288,1352,1455,1556,1621,1684,1744,1815,1937,2062,2182,2250,2337,2413,2489,2582,2678,2745,2809,2862,2920,2970,3031,3091,3153,3217,3279,3338,3403,3469,3533,3600,3654,3713,3786,3859,3913"}, "to": {"startLines": "2,11,15,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,411,638,4749,4844,4941,5023,5120,5218,5295,5359,5462,5563,5628,5691,5751,5822,5944,6069,6189,6257,6344,6420,6496,6589,6685,6752,7545,7598,7656,7706,7767,7827,7889,7953,8015,8074,8139,8205,8269,8336,8390,8449,8522,8595", "endLines": "10,14,18,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111", "endColumns": "17,12,12,94,96,81,96,97,76,63,102,100,64,62,59,70,121,124,119,67,86,75,75,92,95,66,63,52,57,49,60,59,61,63,61,58,64,65,63,66,53,58,72,72,53", "endOffsets": "406,633,837,4839,4936,5018,5115,5213,5290,5354,5457,5558,5623,5686,5746,5817,5939,6064,6184,6252,6339,6415,6491,6584,6680,6747,6811,7593,7651,7701,7762,7822,7884,7948,8010,8069,8134,8200,8264,8331,8385,8444,8517,8590,8644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\dcdce9d9bb74ce2c916a6bbd71ef776f\\transformed\\media3-exoplayer-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,193,270,341,422,504,605,698", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "124,188,265,336,417,499,600,693,779"}, "to": {"startLines": "85,86,87,88,89,90,91,92,93", "startColumns": "4,4,4,4,4,4,4,4,4", "startOffsets": "6816,6890,6954,7031,7102,7183,7265,7366,7459", "endColumns": "73,63,76,70,80,81,100,92,85", "endOffsets": "6885,6949,7026,7097,7178,7260,7361,7454,7540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\92121f0061d84fd6603428dd9555221c\\transformed\\appcompat-1.6.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,919,1010,1103,1198,1292,1392,1485,1580,1674,1765,1856,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "214,326,439,527,634,760,838,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1933,2049,2159,2258,2371,2476,2590,2754,2854,2937"}, "to": {"startLines": "23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,179", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1011,1125,1237,1350,1438,1545,1671,1749,1825,1916,2009,2104,2198,2298,2391,2486,2580,2671,2762,2844,2960,3070,3169,3282,3387,3501,3665,14157", "endColumns": "113,111,112,87,106,125,77,75,90,92,94,93,99,92,94,93,90,90,81,115,109,98,112,104,113,163,99,82", "endOffsets": "1120,1232,1345,1433,1540,1666,1744,1820,1911,2004,2099,2193,2293,2386,2481,2575,2666,2757,2839,2955,3065,3164,3277,3382,3496,3660,3760,14235"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\1916aa273d557d541d17bc9f12ca7920\\transformed\\media3-session-1.2.1\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,126,212,286,357,442,530,622", "endColumns": "70,85,73,70,84,87,91,95", "endOffsets": "121,207,281,352,437,525,617,713"}, "to": {"startLines": "50,59,128,129,130,131,132,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3765,4600,9846,9920,9991,10076,10164,10256", "endColumns": "70,85,73,70,84,87,91,95", "endOffsets": "3831,4681,9915,9986,10071,10159,10251,10347"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.10.2\\transforms\\6f4bdb46ff28ab587a34c53d6687bf99\\transformed\\navigation-ui-2.7.5\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,173", "endColumns": "117,128", "endOffsets": "168,297"}, "to": {"startLines": "176,177", "startColumns": "4,4", "startOffsets": "13828,13946", "endColumns": "117,128", "endOffsets": "13941,14070"}}]}]}